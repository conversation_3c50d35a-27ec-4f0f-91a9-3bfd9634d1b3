[{"id": "f1e2d3c4b5a6", "type": "tab", "label": "空調設備通知系統", "disabled": false, "info": "監控5個空調設備的開啟與關閉狀態，並發送通知到 NotifyHelper、Synology Chat 和 Telegram\n\n設備清單：\n1. 加濕器 (humidifier.rdi_640hhchu_shi_ji)\n2. 全熱交換器 (fan.kpi_253hwquan_re_jiao_huan_qi_air_speed)\n3. 客廳冷氣 (climate.ke_ting_leng_qi)\n4. 主臥冷氣 (climate.zhu_wo_leng_qi)\n5. 次臥冷氣 (climate.ci_wo_leng_qi)"}, {"id": "humidifier_monitor", "type": "server-state-changed", "z": "aircon_flow_tab", "name": "加濕器狀態監控", "server": "home_assistant_server", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "humidifier.rdi_640hhchu_shi_ji", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 150, "y": 100, "wires": [["state_processor"]]}, {"id": "fan_monitor", "type": "server-state-changed", "z": "aircon_flow_tab", "name": "全熱交換器監控", "server": "home_assistant_server", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "fan.kpi_253hwquan_re_jiao_huan_qi_air_speed", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 150, "y": 160, "wires": [["state_processor"]]}, {"id": "living_room_ac_monitor", "type": "server-state-changed", "z": "aircon_flow_tab", "name": "客廳冷氣監控", "server": "home_assistant_server", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "climate.ke_ting_leng_qi", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 150, "y": 220, "wires": [["state_processor"]]}, {"id": "master_bedroom_ac_monitor", "type": "server-state-changed", "z": "aircon_flow_tab", "name": "主臥冷氣監控", "server": "home_assistant_server", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "climate.zhu_wo_leng_qi", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 150, "y": 280, "wires": [["state_processor"]]}, {"id": "second_bedroom_ac_monitor", "type": "server-state-changed", "z": "aircon_flow_tab", "name": "次臥冷氣監控", "server": "home_assistant_server", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "climate.ci_wo_leng_qi", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 150, "y": 340, "wires": [["state_processor"]]}, {"id": "state_processor", "type": "function", "z": "aircon_flow_tab", "name": "狀態處理器", "func": "// 設備名稱對應表\nconst deviceNames = {\n    'humidifier.rdi_640hhchu_shi_ji': '加濕器',\n    'fan.kpi_253hwquan_re_jiao_huan_qi_air_speed': '全熱交換器',\n    'climate.ke_ting_leng_qi': '客廳冷氣',\n    'climate.zhu_wo_leng_qi': '主臥冷氣',\n    'climate.ci_wo_leng_qi': '次臥冷氣'\n};\n\n// 狀態對應表\nconst stateNames = {\n    'on': '開啟',\n    'off': '關閉',\n    'heat': '加熱',\n    'cool': '冷卻',\n    'auto': '自動',\n    'dry': '除濕',\n    'fan_only': '送風',\n    'unavailable': '無法連接',\n    'unknown': '未知狀態'\n};\n\n// 獲取實體ID\nconst entityId = msg.data.entity_id;\nconst newState = msg.data.new_state.state;\nconst oldState = msg.data.old_state ? msg.data.old_state.state : null;\n\n// 檢查是否為有效的狀態變化\nif (!newState || newState === oldState) {\n    return null;\n}\n\n// 獲取設備友好名稱\nconst deviceName = deviceNames[entityId] || entityId;\nconst stateName = stateNames[newState] || newState;\nconst oldStateName = stateNames[oldState] || oldState;\n\n// 檢查是否需要發送通知（避免重複通知）\nconst contextKey = `lastState_${entityId}`;\nconst lastNotifiedState = context.get(contextKey);\n\nif (lastNotifiedState === newState) {\n    return null;\n}\n\n// 更新上次通知的狀態\ncontext.set(contextKey, newState);\n\n// 準備通知訊息\nconst timestamp = new Date().toLocaleString('zh-TW', {\n    timeZone: 'Asia/Taipei',\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit'\n});\n\n// 判斷狀態變化類型\nlet statusIcon = '';\nlet statusColor = '';\n\nif (newState === 'on' || newState === 'heat' || newState === 'cool' || newState === 'auto') {\n    statusIcon = '🟢';\n    statusColor = '#28a745';\n} else if (newState === 'off') {\n    statusIcon = '🔴';\n    statusColor = '#dc3545';\n} else {\n    statusIcon = '🟡';\n    statusColor = '#ffc107';\n}\n\n// 構建訊息內容\nconst message = {\n    deviceName: deviceName,\n    entityId: entityId,\n    newState: stateName,\n    oldState: oldStateName,\n    timestamp: timestamp,\n    statusIcon: statusIcon,\n    statusColor: statusColor,\n    title: `${deviceName}狀態變更`,\n    shortMessage: `${deviceName} ${stateName}`,\n    fullMessage: `${statusIcon} ${deviceName}已${stateName}\\n時間：${timestamp}\\n設備：${entityId}`\n};\n\nmsg.payload = message;\nmsg.topic = 'aircon_notification';\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 400, "y": 220, "wires": [["message_formatter"]]}, {"id": "message_formatter", "type": "function", "z": "aircon_flow_tab", "name": "訊息格式化器", "func": "// 為不同的通知服務格式化訊息\nconst data = msg.payload;\n\n// NotifyHelper 格式\nconst notifyHelperMsg = {\n    topic: 'notify_helper',\n    payload: {\n        title: data.title,\n        message: data.fullMessage,\n        priority: 'normal',\n        sound: 'default'\n    }\n};\n\n// Synology Chat 格式\nconst synologyChatMsg = {\n    topic: 'synology_chat',\n    payload: {\n        text: data.fullMessage,\n        username: 'Home Assistant',\n        icon_emoji: data.statusIcon\n    }\n};\n\n// Telegram 格式\nconst telegramMsg = {\n    topic: 'telegram',\n    payload: {\n        chatId: process.env.TELEGRAM_CHAT_ID || 'YOUR_CHAT_ID',\n        type: 'message',\n        content: data.fullMessage,\n        options: {\n            parse_mode: 'HTML',\n            disable_notification: false\n        }\n    }\n};\n\n// 返回三個輸出\nreturn [notifyHelperMsg, synologyChatMsg, telegramMsg];", "outputs": 3, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 600, "y": 220, "wires": [["notify_helper_sender"], ["synology_chat_sender"], ["telegram_sender"]]}, {"id": "notify_helper_sender", "type": "api-call-service", "z": "aircon_flow_tab", "name": "NotifyHelper 發送", "server": "home_assistant_server", "version": 5, "debugenabled": false, "domain": "notify", "service": "notify", "areaId": [], "deviceId": [], "entityId": [], "data": "{\"title\": \"{{payload.title}}\", \"message\": \"{{payload.message}}\"}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 850, "y": 160, "wires": [["notification_logger"]]}, {"id": "synology_chat_sender", "type": "http request", "z": "aircon_flow_tab", "name": "Synology Chat 發送", "method": "POST", "ret": "txt", "paytoqs": "ignore", "url": "YOUR_SYNOLOGY_CHAT_WEBHOOK_URL", "tls": "", "persist": false, "proxy": "", "authType": "", "senderr": false, "headers": [{"keyType": "other", "keyValue": "Content-Type", "valueType": "other", "valueValue": "application/json"}], "x": 850, "y": 220, "wires": [["notification_logger"]]}, {"id": "telegram_sender", "type": "telegram sender", "z": "aircon_flow_tab", "name": "Telegram 發送", "bot": "telegram_bot", "hasenabled": false, "outputs": 1, "x": 850, "y": 280, "wires": [["notification_logger"]]}, {"id": "notification_logger", "type": "debug", "z": "aircon_flow_tab", "name": "通知日誌", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1100, "y": 220, "wires": []}, {"id": "error_handler", "type": "catch", "z": "aircon_flow_tab", "name": "錯誤處理", "scope": null, "uncaught": false, "x": 1100, "y": 320, "wires": [["error_logger"]]}, {"id": "error_logger", "type": "debug", "z": "aircon_flow_tab", "name": "錯誤日誌", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1300, "y": 320, "wires": []}, {"id": "home_assistant_server", "type": "server", "name": "Home Assistant", "version": 4, "addon": true, "rejectUnauthorizedCerts": true, "ha_boolean": "y|yes|true|on|home|open", "connectionDelay": true, "cacheJson": true, "heartbeat": false, "heartbeatInterval": 30}, {"id": "telegram_bot", "type": "telegram bot", "botname": "AirCon Notification Bot", "usernames": "", "chatids": "", "baseapiurl": "", "updatemode": "polling", "pollinterval": "300", "usesocks": false, "sockshost": "", "socksprotocol": "socks5", "socksport": "6667", "socksusername": "anonymous", "sockspassword": "", "bothost": "", "botpath": "", "localbotport": "8443", "publicbotport": "8443", "privatekey": "", "certificate": "", "useselfsignedcertificate": false, "sslterminated": false, "verboselogging": false}]