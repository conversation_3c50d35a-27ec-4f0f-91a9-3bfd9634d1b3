# 空調設備通知系統環境變數配置範例
# 複製此文件為 .env 並填入您的實際配置值

# ===========================================
# Home Assistant 配置
# ===========================================
HA_BASE_URL=http://192.168.1.100:8123
HA_ACCESS_TOKEN=your_long_lived_access_token_here

# ===========================================
# Telegram Bot 配置
# ===========================================
# 從 @BotFather 獲取的 Bot Token
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz

# 您的 Telegram Chat ID（可以是個人或群組）
TELEGRAM_CHAT_ID=123456789

# 可選：Telegram API 基礎 URL（通常不需要修改）
TELEGRAM_API_URL=https://api.telegram.org

# ===========================================
# Synology Chat 配置
# ===========================================
# Synology Chat Incoming Webhook URL
SYNOLOGY_CHAT_WEBHOOK=https://your-synology-ip:5001/webapi/entry.cgi?api=SYNO.Chat.External&method=incoming&version=2&token=YOUR_TOKEN

# Synology Chat 頻道名稱
SYNOLOGY_CHAT_CHANNEL=#smart-home

# ===========================================
# NotifyHelper 配置
# ===========================================
# Home Assistant 通知服務名稱
NOTIFY_SERVICE=notify.notify

# 可選：特定的通知服務（如 notify.mobile_app_your_phone）
# NOTIFY_SERVICE=notify.mobile_app_iphone

# ===========================================
# 系統配置
# ===========================================
# 時區設定
TIMEZONE=Asia/Taipei

# 語言設定
LANGUAGE=zh-TW

# 日誌等級 (debug, info, warn, error)
LOG_LEVEL=info

# 通知冷卻時間（秒）- 防止重複通知
NOTIFICATION_COOLDOWN=60

# ===========================================
# 進階配置
# ===========================================
# 靜音時間開始（24小時制）
QUIET_HOURS_START=23:00

# 靜音時間結束（24小時制）
QUIET_HOURS_END=07:00

# 是否啟用靜音時間
QUIET_HOURS_ENABLED=true

# HTTP 請求超時時間（毫秒）
HTTP_TIMEOUT=5000

# 重試次數
MAX_RETRIES=3

# ===========================================
# 安全性配置
# ===========================================
# 是否驗證 SSL 憑證
VERIFY_SSL=true

# 是否記錄敏感資訊（建議設為 false）
LOG_SENSITIVE_DATA=false

# ===========================================
# 設備特定配置
# ===========================================
# 是否啟用加濕器通知
HUMIDIFIER_NOTIFICATIONS=true

# 是否啟用風扇通知
FAN_NOTIFICATIONS=true

# 是否啟用冷氣通知
CLIMATE_NOTIFICATIONS=true

# ===========================================
# 測試模式配置
# ===========================================
# 測試模式（不會實際發送通知，只記錄日誌）
TEST_MODE=false

# 測試用的 Chat ID
TEST_CHAT_ID=your_test_chat_id

# ===========================================
# 備份和恢復
# ===========================================
# 自動備份配置的間隔（小時）
AUTO_BACKUP_INTERVAL=24

# 備份保留天數
BACKUP_RETENTION_DAYS=30

# 備份存儲路徑
BACKUP_PATH=/data/backups
