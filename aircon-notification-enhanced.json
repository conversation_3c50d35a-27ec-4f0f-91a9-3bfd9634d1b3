[{"id": "f1e2d3c4b5a6", "type": "tab", "label": "空調設備通知系統 - 增強版", "disabled": false, "info": "監控5個空調設備的所有狀態變化，包括開關、模式、溫度等，並發送通知到 NotifyHelper、Synology Chat 和 Telegram\n\n設備清單：\n1. 吊隱除濕機 (humidifier.rdi_640hhchu_shi_ji)\n2. 全熱交換器 (fan.kpi_253hwquan_re_jiao_huan_qi_air_speed)\n3. 客廳冷氣 (climate.ke_ting_leng_qi)\n4. 主臥冷氣 (climate.zhu_wo_leng_qi)\n5. 次臥冷氣 (climate.ci_wo_leng_qi)", "env": []}, {"id": "a1b2c3d4e5f6", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "吊隱除濕機狀態監控", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["humidifier.rdi_640hhchu_shi_ji"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 160, "y": 100, "wires": [["state_processor"]]}, {"id": "b2c3d4e5f6a1", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "全熱交換器監控", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["fan.kpi_253hwquan_re_jiao_huan_qi_air_speed"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 160, "y": 160, "wires": [["state_processor"]]}, {"id": "c3d4e5f6a1b2", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "客廳冷氣監控", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["climate.ke_ting_leng_qi"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 160, "y": 220, "wires": [["state_processor"]]}, {"id": "d4e5f6a1b2c3", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "主臥冷氣監控", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["climate.zhu_wo_leng_qi"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 160, "y": 280, "wires": [["state_processor"]]}, {"id": "e5f6a1b2c3d4", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "次臥冷氣監控", "server": "125d83f7.1a33dc", "version": 6, "outputs": 1, "exposeAsEntityConfig": "", "entities": {"entity": ["climate.ci_wo_leng_qi"], "substring": [], "regex": []}, "outputInitially": false, "stateType": "str", "ifState": "", "ifStateType": "str", "ifStateOperator": "is", "outputOnlyOnStateChange": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}, {"property": "topic", "propertyType": "msg", "value": "", "valueType": "triggerId"}], "x": 160, "y": 340, "wires": [["state_processor"]]}, {"id": "state_processor", "type": "function", "z": "f1e2d3c4b5a6", "name": "增強狀態處理器", "func": "// 設備名稱對應表\nconst deviceNames = {\n    'humidifier.rdi_640hhchu_shi_ji': '吊隱除濕機',\n    'fan.kpi_253hwquan_re_jiao_huan_qi_air_speed': '全熱交換器',\n    'climate.ke_ting_leng_qi': '客廳冷氣',\n    'climate.zhu_wo_leng_qi': '主臥冷氣',\n    'climate.ci_wo_leng_qi': '次臥冷氣'\n};\n\n// 位置對應表\nconst deviceLocations = {\n    'humidifier.rdi_640hhchu_shi_ji': '客廳',\n    'fan.kpi_253hwquan_re_jiao_huan_qi_air_speed': '全屋',\n    'climate.ke_ting_leng_qi': '客廳',\n    'climate.zhu_wo_leng_qi': '主臥室',\n    'climate.ci_wo_leng_qi': '次臥室'\n};\n\n// 狀態對應表\nconst stateNames = {\n    'on': '開啟',\n    'off': '關閉',\n    'heat': '暖氣模式',\n    'cool': '冷氣模式',\n    'auto': '自動模式',\n    'dry': '除濕模式',\n    'fan_only': '送風模式',\n    'heat_cool': '冷暖模式',\n    'low': '低速',\n    'medium': '中速',\n    'high': '高速',\n    'unavailable': '無法連接',\n    'unknown': '未知狀態'\n};\n\n// 狀態圖示對應\nconst statusIcons = {\n    'on': '🟢',\n    'off': '🔴',\n    'heat': '🔥',\n    'cool': '❄️',\n    'auto': '🔄',\n    'dry': '💨',\n    'fan_only': '🌪️',\n    'heat_cool': '🌡️',\n    'low': '🟢',\n    'medium': '🟡',\n    'high': '🔴',\n    'unavailable': '⚠️',\n    'unknown': '❓'\n};\n\n// 獲取實體資訊\nconst entityId = msg.data.entity_id;\nconst newState = msg.data.new_state;\nconst oldState = msg.data.old_state;\n\nif (!newState || !oldState) {\n    return null;\n}\n\n// 檢查狀態變化\nconst stateChanged = newState.state !== oldState.state;\n\n// 檢查重要屬性變化（針對 climate 實體）\nlet attributeChanges = [];\nif (entityId.startsWith('climate.')) {\n    const newAttrs = newState.attributes || {};\n    const oldAttrs = oldState.attributes || {};\n    \n    // 檢查溫度設定變化\n    if (newAttrs.temperature !== oldAttrs.temperature) {\n        attributeChanges.push({\n            type: 'temperature',\n            old: oldAttrs.temperature,\n            new: newAttrs.temperature\n        });\n    }\n    \n    // 檢查風扇模式變化\n    if (newAttrs.fan_mode !== oldAttrs.fan_mode) {\n        attributeChanges.push({\n            type: 'fan_mode',\n            old: oldAttrs.fan_mode,\n            new: newAttrs.fan_mode\n        });\n    }\n    \n    // 檢查擺風模式變化\n    if (newAttrs.swing_mode !== oldAttrs.swing_mode) {\n        attributeChanges.push({\n            type: 'swing_mode',\n            old: oldAttrs.swing_mode,\n            new: newAttrs.swing_mode\n        });\n    }\n}\n\n// 如果沒有任何變化，不發送通知\nif (!stateChanged && attributeChanges.length === 0) {\n    return null;\n}\n\n// 防重複通知檢查\nconst contextKey = `lastNotification_${entityId}`;\nconst lastNotification = context.get(contextKey) || {};\nconst currentTime = Date.now();\n\n// 檢查是否在冷卻期內（60秒）\nif (lastNotification.time && (currentTime - lastNotification.time) < 60000) {\n    const lastStateStr = JSON.stringify({\n        state: lastNotification.state,\n        attrs: lastNotification.attrs\n    });\n    const currentStateStr = JSON.stringify({\n        state: newState.state,\n        attrs: {\n            temperature: newState.attributes?.temperature,\n            fan_mode: newState.attributes?.fan_mode,\n            swing_mode: newState.attributes?.swing_mode\n        }\n    });\n    \n    if (lastStateStr === currentStateStr) {\n        return null;\n    }\n}\n\n// 更新上次通知記錄\ncontext.set(contextKey, {\n    time: currentTime,\n    state: newState.state,\n    attrs: {\n        temperature: newState.attributes?.temperature,\n        fan_mode: newState.attributes?.fan_mode,\n        swing_mode: newState.attributes?.swing_mode\n    }\n});\n\n// 獲取設備資訊\nconst deviceName = deviceNames[entityId] || entityId;\nconst location = deviceLocations[entityId] || '未知位置';\nconst stateName = stateNames[newState.state] || newState.state;\nconst oldStateName = stateNames[oldState.state] || oldState.state;\nconst statusIcon = statusIcons[newState.state] || '🟡';\n\n// 生成時間戳記\nconst timestamp = new Date().toLocaleString('zh-TW', {\n    timeZone: 'Asia/Taipei',\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit'\n});\n\n// 構建變化訊息\nlet changeMessages = [];\n\n// 狀態變化訊息\nif (stateChanged) {\n    changeMessages.push(`模式：${stateName}`);\n}\n\n// 屬性變化訊息\nattributeChanges.forEach(change => {\n    switch (change.type) {\n        case 'temperature':\n            changeMessages.push(`設定溫度：${change.new}°C`);\n            break;\n        case 'fan_mode':\n            const fanModeNames = {\n                'auto': '自動風速',\n                'low': '低風速',\n                'medium': '中風速',\n                'high': '高風速',\n                'quiet': '靜音模式'\n            };\n            changeMessages.push(`風速：${fanModeNames[change.new] || change.new}`);\n            break;\n        case 'swing_mode':\n            const swingModeNames = {\n                'off': '關閉擺風',\n                'vertical': '垂直擺風',\n                'horizontal': '水平擺風',\n                'both': '全方向擺風'\n            };\n            changeMessages.push(`擺風：${swingModeNames[change.new] || change.new}`);\n            break;\n    }\n});\n\n// 獲取當前溫度（如果有的話）\nlet currentTempInfo = '';\nif (newState.attributes?.current_temperature) {\n    currentTempInfo = `\\n目前溫度：${newState.attributes.current_temperature}°C`;\n}\n\n// 構建完整訊息\nconst fullMessage = `${timestamp}\\n${statusIcon} ${deviceName}\\n位置：${location}\\n${changeMessages.join('\\n')}${currentTempInfo}`;\n\nconst message = {\n    deviceName: deviceName,\n    location: location,\n    entityId: entityId,\n    newState: stateName,\n    oldState: oldStateName,\n    timestamp: timestamp,\n    statusIcon: statusIcon,\n    title: `${deviceName}狀態變更`,\n    shortMessage: `${deviceName} ${changeMessages.join(', ')}`,\n    fullMessage: fullMessage,\n    changes: changeMessages\n};\n\nmsg.payload = message;\nmsg.topic = 'aircon_notification';\n\n// 記錄到除錯日誌\nnode.log(`設備狀態變更: ${deviceName} (${entityId}) - ${changeMessages.join(', ')}`);\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 220, "wires": [["message_formatter"]]}, {"id": "message_formatter", "type": "function", "z": "f1e2d3c4b5a6", "name": "建立通知訊息", "func": "const data = msg.payload;\nconst message = data.fullMessage;\nconst title = data.title;\n\n// NotifyHelper 格式 - 修正版本\nmsg.payload_notifyhelper = {\n    message: message,\n    title: title\n};\n\n// Synology Chat 格式\nmsg.payload_synology_chat = {\n    message: message\n};\n\n// Telegram 格式\nmsg.payload_telegram = {\n    message: message\n};\n\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 640, "y": 220, "wires": [["delay_notify", "delay_synology", "delay_telegram"]]}, {"id": "delay_notify", "type": "delay", "z": "f1e2d3c4b5a6", "name": "", "pauseType": "delay", "timeout": "1", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 860, "y": 160, "wires": [["notify_helper_sender"]]}, {"id": "delay_synology", "type": "delay", "z": "f1e2d3c4b5a6", "name": "", "pauseType": "delay", "timeout": "1", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 860, "y": 220, "wires": [["synology_chat_sender"]]}, {"id": "delay_telegram", "type": "delay", "z": "f1e2d3c4b5a6", "name": "", "pauseType": "delay", "timeout": "1", "timeoutUnits": "seconds", "rate": "1", "nbRateUnits": "1", "rateUnits": "second", "randomFirst": "1", "randomLast": "5", "randomUnits": "seconds", "drop": false, "allowrate": false, "outputs": 1, "x": 860, "y": 280, "wires": [["telegram_sender"]]}, {"id": "notify_helper_sender", "type": "api-call-service", "z": "f1e2d3c4b5a6", "name": "NotifyHelper通知", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.notify_person", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"message\": payload_notifyhelper.message, \"title\": payload_notifyhelper.title, \"targets\": [\"person.ming\"]}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": true, "domain": "notify", "service": "notify_person", "x": 1080, "y": 160, "wires": [["debug_notify"]]}, {"id": "synology_chat_sender", "type": "api-call-service", "z": "f1e2d3c4b5a6", "name": "Synology Chat通知", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.synology_chat_bot_3", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"message\": payload_synology_chat.message}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": true, "domain": "notify", "service": "synology_chat_bot_3", "x": 1080, "y": 220, "wires": [["debug_synology"]]}, {"id": "telegram_sender", "type": "api-call-service", "z": "f1e2d3c4b5a6", "name": "發送Telegram通知", "server": "125d83f7.1a33dc", "version": 7, "debugenabled": false, "action": "notify.telegram", "floorId": [], "areaId": [], "deviceId": [], "entityId": [], "labelId": [], "data": "{\"message\": payload_telegram.message}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "blockInputOverrides": true, "domain": "notify", "service": "telegram", "x": 1080, "y": 280, "wires": [["debug_telegram"]]}, {"id": "debug_notify", "type": "debug", "z": "f1e2d3c4b5a6", "name": "NotifyHelper除錯", "active": true, "tosidebar": true, "console": true, "tostatus": true, "complete": "payload", "targetType": "msg", "statusVal": "payload", "statusType": "auto", "x": 1320, "y": 160, "wires": []}, {"id": "debug_synology", "type": "debug", "z": "f1e2d3c4b5a6", "name": "Synology除錯", "active": true, "tosidebar": true, "console": true, "tostatus": true, "complete": "payload", "targetType": "msg", "statusVal": "payload", "statusType": "auto", "x": 1320, "y": 220, "wires": []}, {"id": "debug_telegram", "type": "debug", "z": "f1e2d3c4b5a6", "name": "Telegram除錯", "active": true, "tosidebar": true, "console": true, "tostatus": true, "complete": "payload", "targetType": "msg", "statusVal": "payload", "statusType": "auto", "x": 1320, "y": 280, "wires": []}, {"id": "manual_test_inject", "type": "inject", "z": "f1e2d3c4b5a6", "name": "測試冷氣模式變更", "props": [{"p": "payload"}, {"p": "data"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "test", "payloadType": "str", "data": "{\"entity_id\":\"climate.ke_ting_leng_qi\",\"new_state\":{\"state\":\"cool\",\"attributes\":{\"temperature\":24,\"current_temperature\":26,\"fan_mode\":\"auto\",\"swing_mode\":\"off\"}},\"old_state\":{\"state\":\"off\",\"attributes\":{\"temperature\":25,\"current_temperature\":26,\"fan_mode\":\"low\",\"swing_mode\":\"off\"}}}", "dataType": "json", "x": 160, "y": 400, "wires": [["state_processor"]]}, {"id": "manual_test_inject2", "type": "inject", "z": "f1e2d3c4b5a6", "name": "測試溫度調整", "props": [{"p": "payload"}, {"p": "data"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "test", "payloadType": "str", "data": "{\"entity_id\":\"climate.zhu_wo_leng_qi\",\"new_state\":{\"state\":\"cool\",\"attributes\":{\"temperature\":22,\"current_temperature\":25,\"fan_mode\":\"high\",\"swing_mode\":\"vertical\"}},\"old_state\":{\"state\":\"cool\",\"attributes\":{\"temperature\":24,\"current_temperature\":25,\"fan_mode\":\"auto\",\"swing_mode\":\"off\"}}}", "dataType": "json", "x": 160, "y": 460, "wires": [["state_processor"]]}, {"id": "manual_test_inject3", "type": "inject", "z": "f1e2d3c4b5a6", "name": "測試除濕機開關", "props": [{"p": "payload"}, {"p": "data"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "test", "payloadType": "str", "data": "{\"entity_id\":\"humidifier.rdi_640hhchu_shi_ji\",\"new_state\":{\"state\":\"on\",\"attributes\":{}},\"old_state\":{\"state\":\"off\",\"attributes\":{}}}", "dataType": "json", "x": 160, "y": 520, "wires": [["state_processor"]]}, {"id": "error_handler", "type": "catch", "z": "f1e2d3c4b5a6", "name": "錯誤處理", "scope": null, "uncaught": false, "x": 1080, "y": 360, "wires": [["error_logger"]]}, {"id": "error_logger", "type": "debug", "z": "f1e2d3c4b5a6", "name": "錯誤日誌", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1280, "y": 360, "wires": []}, {"id": "125d83f7.1a33dc", "type": "server", "name": "Home Assistant", "addon": true}]