# Lab 項目集合

這個倉庫包含多個實用的項目和工具。

## 🏠 空調設備通知系統

基於 Node-RED 的智能家居空調設備監控和通知系統，支援多種通知方式。

### 監控設備
- 加濕器 (`humidifier.rdi_640hhchu_shi_ji`)
- 全熱交換器 (`fan.kpi_253hwquan_re_jiao_huan_qi_air_speed`)
- 客廳冷氣 (`climate.ke_ting_leng_qi`)
- 主臥冷氣 (`climate.zhu_wo_leng_qi`)
- 次臥冷氣 (`climate.ci_wo_leng_qi`)

### 通知方式
- NotifyHelper (Home Assistant 內建)
- Synology Chat (群暉聊天室)
- Telegram Bot

### 快速開始
1. 執行安裝腳本：`install-aircon-notifications.bat` (Windows) 或 `install-aircon-notifications.sh` (Linux/Mac)
2. 編輯 `.env` 文件配置您的服務
3. 在 Node-RED 中導入 `aircon-notification-flow.json`
4. 執行測試：`node test-notifications.js`

詳細說明請參考：[README-aircon-notifications.md](README-aircon-notifications.md)

---

## 🎨 3D字母展开图生成器

这是一个专业的3D字母展开图生成工具，基于真实的纸工艺设计原理，可以生成可直接制作的立体英文字母。

## 🆕 专业版特点

- 🎨 **完整支持26个英文字母**（A-Z全部可用）
- 📐 **专业展开图设计** - 基于真实纸工艺原理
- 🔧 **精确的折叠和粘贴指导** - 实线裁剪，虚线折叠
- 📏 **可调节参数** - 字母大小（150px-400px）和厚度（15px-60px）
- 🖨️ **打印优化** - 适配A4/A3纸张
- 📋 **详细制作说明** - 从打印到完成的全流程指导
- 🎯 **胶水标签系统** - 明确标示粘贴位置

## 文件说明

- `professional-3d-generator.html` - **专业版生成器**（推荐使用）
- `example-letter-E.html` - 字母E的完整示例展开图
- `3d-alphabet-generator.html` - 基础版生成器
- `letter-templates.js` - 字母模板数据文件
- `README.md` - 使用说明文档

## 使用方法

### 专业版使用（推荐）

1. **打开工具**：在浏览器中打开 `professional-3d-generator.html`
2. **选择字母**：点击您想要制作的字母按钮（支持A-Z全部字母）
3. **调整参数**：使用滑块调整字母大小和厚度
4. **生成展开图**：点击"生成专业展开图"按钮
5. **打印**：点击"打印展开图"按钮打印到A4纸上

### 快速体验

- 打开 `example-letter-E.html` 查看字母E的完整示例
- 可直接打印制作，体验完整流程

## 制作步骤

### 材料准备
- A4打印纸（建议使用较厚的卡纸）
- 剪刀或美工刀
- 胶水或双面胶
- 尺子（可选，用于折叠）

### 制作过程

1. **打印展开图**
   - 使用A4纸打印生成的展开图
   - 确保打印比例为100%（不要缩放）

2. **裁剪**
   - 沿着**实线**裁剪字母的轮廓
   - 保留所有的胶水标签（黄色部分）

3. **折叠**
   - 沿着**虚线**向内折叠
   - 使用尺子压出清晰的折痕

4. **粘贴**
   - 在标有"胶水"的黄色标签上涂抹胶水
   - 按照编号顺序将侧面条带粘贴到正面和背面
   - 确保边缘对齐

5. **完成**
   - 等待胶水完全干燥
   - 检查所有接缝是否牢固
   - 您的3D字母就完成了！

## 文件说明

- `3d-alphabet-generator.html` - 主要的网页工具
- `letter-templates.js` - 字母模板数据文件
- `README.md` - 使用说明文档

## 技术特点

- 使用SVG技术生成精确的展开图
- 响应式设计，支持不同屏幕尺寸
- 打印优化，确保最佳打印效果
- 模块化设计，易于扩展新字母

## 小贴士

- 🎯 **选择合适的纸张**：较厚的卡纸（160-200g）效果最佳
- 📐 **精确折叠**：使用尺子压出清晰的折痕
- 🔧 **胶水用量**：少量多次，避免胶水溢出
- ⏰ **耐心等待**：让胶水充分干燥再进行下一步
- 🎨 **个性化**：可以在制作前给字母涂色

## 故障排除

**Q: 打印出来的尺寸不对？**
A: 确保打印设置中选择"实际大小"或"100%"，不要选择"适合页面"。

**Q: 折叠线看不清楚？**
A: 虚线可能在某些打印机上不够明显，可以用铅笔轻轻描一下。

**Q: 粘贴时对不齐？**
A: 先不要涂胶水，干燥状态下试拼装，确认位置后再粘贴。

**Q: 字母不够立体？**
A: 检查所有折叠线是否都已正确折叠，增加厚度参数可以让字母更立体。

## 扩展想法

- 制作整个单词或句子
- 使用不同颜色的纸张
- 添加LED灯带制作发光字母
- 制作数字和符号
- 创建字母收纳盒

享受您的3D字母制作过程！🎉
