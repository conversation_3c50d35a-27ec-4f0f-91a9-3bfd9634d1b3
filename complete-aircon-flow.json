[{"id": "f1e2d3c4b5a6", "type": "tab", "label": "空調設備通知系統", "disabled": false, "info": "監控5個空調設備的開啟與關閉狀態，並發送通知到 NotifyHelper、Synology Chat 和 Telegram\n\n設備清單：\n1. 加濕器 (humidifier.rdi_640hhchu_shi_ji)\n2. 全熱交換器 (fan.kpi_253hwquan_re_jiao_huan_qi_air_speed)\n3. 客廳冷氣 (climate.ke_ting_leng_qi)\n4. 主臥冷氣 (climate.zhu_wo_leng_qi)\n5. 次臥冷氣 (climate.ci_wo_leng_qi)"}, {"id": "a1b2c3d4e5f6", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "加濕器狀態監控", "server": "ha_server_config", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "humidifier.rdi_640hhchu_shi_ji", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 160, "y": 100, "wires": [["state_processor"]]}, {"id": "b2c3d4e5f6a1", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "全熱交換器監控", "server": "ha_server_config", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "fan.kpi_253hwquan_re_jiao_huan_qi_air_speed", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 160, "y": 160, "wires": [["state_processor"]]}, {"id": "c3d4e5f6a1b2", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "客廳冷氣監控", "server": "ha_server_config", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "climate.ke_ting_leng_qi", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 160, "y": 220, "wires": [["state_processor"]]}, {"id": "d4e5f6a1b2c3", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "主臥冷氣監控", "server": "ha_server_config", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "climate.zhu_wo_leng_qi", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 160, "y": 280, "wires": [["state_processor"]]}, {"id": "e5f6a1b2c3d4", "type": "server-state-changed", "z": "f1e2d3c4b5a6", "name": "次臥冷氣監控", "server": "ha_server_config", "version": 4, "exposeToHomeAssistant": false, "haConfig": [], "entityidfilter": "climate.ci_wo_leng_qi", "entityidfiltertype": "exact", "outputinitially": false, "state_type": "str", "haltifstate": "", "halt_if_type": "str", "halt_if_compare": "is", "outputs": 1, "output_only_on_state_change": true, "for": "0", "forType": "num", "forUnits": "minutes", "ignorePrevStateNull": false, "ignorePrevStateUnknown": false, "ignorePrevStateUnavailable": false, "ignoreCurrentStateUnknown": false, "ignoreCurrentStateUnavailable": false, "outputProperties": [{"property": "payload", "propertyType": "msg", "value": "", "valueType": "entityState"}, {"property": "data", "propertyType": "msg", "value": "", "valueType": "eventData"}], "x": 160, "y": 340, "wires": [["state_processor"]]}, {"id": "state_processor", "type": "function", "z": "f1e2d3c4b5a6", "name": "狀態處理器", "func": "// 設備名稱對應表\nconst deviceNames = {\n    'humidifier.rdi_640hhchu_shi_ji': '加濕器',\n    'fan.kpi_253hwquan_re_jiao_huan_qi_air_speed': '全熱交換器',\n    'climate.ke_ting_leng_qi': '客廳冷氣',\n    'climate.zhu_wo_leng_qi': '主臥冷氣',\n    'climate.ci_wo_leng_qi': '次臥冷氣'\n};\n\n// 位置對應表\nconst deviceLocations = {\n    'humidifier.rdi_640hhchu_shi_ji': '客廳',\n    'fan.kpi_253hwquan_re_jiao_huan_qi_air_speed': '全屋',\n    'climate.ke_ting_leng_qi': '客廳',\n    'climate.zhu_wo_leng_qi': '主臥室',\n    'climate.ci_wo_leng_qi': '次臥室'\n};\n\n// 狀態對應表\nconst stateNames = {\n    'on': '開啟',\n    'off': '關閉',\n    'heat': '加熱模式',\n    'cool': '冷卻模式',\n    'auto': '自動模式',\n    'dry': '除濕模式',\n    'fan_only': '送風模式',\n    'low': '低速',\n    'medium': '中速',\n    'high': '高速',\n    'unavailable': '無法連接',\n    'unknown': '未知狀態'\n};\n\n// 狀態圖示對應\nconst statusIcons = {\n    'on': '🟢',\n    'off': '🔴',\n    'heat': '🔥',\n    'cool': '❄️',\n    'auto': '🔄',\n    'dry': '💨',\n    'fan_only': '🌪️',\n    'low': '🟢',\n    'medium': '🟡',\n    'high': '🔴',\n    'unavailable': '⚠️',\n    'unknown': '❓'\n};\n\n// 獲取實體資訊\nconst entityId = msg.data.entity_id;\nconst newState = msg.data.new_state.state;\nconst oldState = msg.data.old_state ? msg.data.old_state.state : null;\n\n// 檢查是否為有效的狀態變化\nif (!newState || newState === oldState) {\n    return null;\n}\n\n// 檢查是否需要發送通知（避免重複通知）\nconst contextKey = `lastState_${entityId}`;\nconst lastNotifiedState = context.get(contextKey);\n\nif (lastNotifiedState === newState) {\n    return null;\n}\n\n// 更新上次通知的狀態\ncontext.set(contextKey, newState);\n\n// 獲取設備資訊\nconst deviceName = deviceNames[entityId] || entityId;\nconst location = deviceLocations[entityId] || '未知位置';\nconst stateName = stateNames[newState] || newState;\nconst oldStateName = stateNames[oldState] || oldState;\nconst statusIcon = statusIcons[newState] || '🟡';\n\n// 生成時間戳記\nconst timestamp = new Date().toLocaleString('zh-TW', {\n    timeZone: 'Asia/Taipei',\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit'\n});\n\n// 構建訊息內容\nconst message = {\n    deviceName: deviceName,\n    location: location,\n    entityId: entityId,\n    newState: stateName,\n    oldState: oldStateName,\n    timestamp: timestamp,\n    statusIcon: statusIcon,\n    title: `${deviceName}狀態變更`,\n    shortMessage: `${deviceName} ${stateName}`,\n    fullMessage: `${statusIcon} ${deviceName}已${stateName}\\n📍 位置：${location}\\n🕐 時間：${timestamp}`\n};\n\nmsg.payload = message;\nmsg.topic = 'aircon_notification';\n\n// 記錄到除錯日誌\nnode.log(`設備狀態變更: ${deviceName} (${entityId}) 從 ${oldStateName} 變更為 ${stateName}`);\n\nreturn msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 220, "wires": [["message_formatter"]]}, {"id": "message_formatter", "type": "function", "z": "f1e2d3c4b5a6", "name": "訊息格式化器", "func": "// 獲取訊息資料\nconst data = msg.payload;\n\n// NotifyHelper 格式\nconst notifyHelperMsg = {\n    topic: 'notify_helper',\n    payload: {\n        service: 'notify.notify',\n        data: {\n            title: data.title,\n            message: data.fullMessage\n        }\n    }\n};\n\n// Synology Chat 格式\nconst synologyChatMsg = {\n    topic: 'synology_chat',\n    payload: {\n        text: `**${data.title}**\\n\\n${data.fullMessage}`,\n        username: 'Smart Home Bot',\n        icon_emoji: data.statusIcon\n    },\n    headers: {\n        'Content-Type': 'application/json'\n    }\n};\n\n// Telegram 格式\nconst telegramMsg = {\n    topic: 'telegram',\n    payload: {\n        chatId: process.env.TELEGRAM_CHAT_ID || 'YOUR_CHAT_ID',\n        type: 'message',\n        content: `<b>${data.statusIcon} ${data.deviceName}</b>\\n\\n狀態：${data.newState}\\n位置：${data.location}\\n時間：${data.timestamp}\\n設備：<code>${data.entityId}</code>`,\n        options: {\n            parse_mode: 'HTML',\n            disable_notification: false\n        }\n    }\n};\n\n// 返回三個輸出\nreturn [notifyHelperMsg, synologyChatMsg, telegramMsg];", "outputs": 3, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 640, "y": 220, "wires": [["notify_helper_sender"], ["synology_chat_sender"], ["telegram_sender"]]}, {"id": "notify_helper_sender", "type": "api-call-service", "z": "f1e2d3c4b5a6", "name": "NotifyHelper 發送", "server": "ha_server_config", "version": 5, "debugenabled": false, "domain": "notify", "service": "notify", "areaId": [], "deviceId": [], "entityId": [], "data": "{\"title\": payload.data.title, \"message\": payload.data.message}", "dataType": "jsonata", "mergeContext": "", "mustacheAltTags": false, "outputProperties": [], "queue": "none", "x": 900, "y": 160, "wires": [["notification_logger"]]}, {"id": "synology_chat_sender", "type": "http request", "z": "f1e2d3c4b5a6", "name": "Synology Chat 發送", "method": "POST", "ret": "txt", "paytoqs": "ignore", "url": "YOUR_SYNOLOGY_CHAT_WEBHOOK_URL", "tls": "", "persist": false, "proxy": "", "authType": "", "senderr": false, "headers": [{"keyType": "other", "keyValue": "Content-Type", "valueType": "other", "valueValue": "application/json"}], "x": 900, "y": 220, "wires": [["notification_logger"]]}, {"id": "telegram_sender", "type": "telegram sender", "z": "f1e2d3c4b5a6", "name": "Telegram 發送", "bot": "telegram_bot_config", "hasenabled": false, "outputs": 1, "x": 900, "y": 280, "wires": [["notification_logger"]]}, {"id": "notification_logger", "type": "debug", "z": "f1e2d3c4b5a6", "name": "通知日誌", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1140, "y": 220, "wires": []}, {"id": "error_handler", "type": "catch", "z": "f1e2d3c4b5a6", "name": "錯誤處理", "scope": null, "uncaught": false, "x": 1140, "y": 320, "wires": [["error_logger"]]}, {"id": "error_logger", "type": "debug", "z": "f1e2d3c4b5a6", "name": "錯誤日誌", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1340, "y": 320, "wires": []}, {"id": "manual_test_inject", "type": "inject", "z": "f1e2d3c4b5a6", "name": "手動測試", "props": [{"p": "payload"}, {"p": "data"}], "repeat": "", "crontab": "", "once": false, "onceDelay": 0.1, "topic": "", "payload": "test", "payloadType": "str", "data": "{\"entity_id\":\"climate.ke_ting_leng_qi\",\"new_state\":{\"state\":\"cool\"},\"old_state\":{\"state\":\"off\"}}", "dataType": "json", "x": 160, "y": 400, "wires": [["state_processor"]]}, {"id": "ha_server_config", "type": "server", "name": "Home Assistant", "version": 4, "addon": true, "rejectUnauthorizedCerts": true, "ha_boolean": "y|yes|true|on|home|open", "connectionDelay": true, "cacheJson": true, "heartbeat": false, "heartbeatInterval": 30, "areaSelector": "friendlyName", "deviceSelector": "friendlyName", "entitySelector": "friendlyName", "statusSeparator": "at: ", "statusYear": "hidden", "statusMonth": "short", "statusDay": "numeric", "statusHourCycle": "h23", "statusTimeFormat": "h:m"}, {"id": "telegram_bot_config", "type": "telegram bot", "botname": "AirCon Notification Bot", "usernames": "", "chatids": "", "baseapiurl": "", "updatemode": "polling", "pollinterval": "300", "usesocks": false, "sockshost": "", "socksprotocol": "socks5", "socksport": "6667", "socksusername": "anonymous", "sockspassword": "", "bothost": "", "botpath": "", "localbotport": "8443", "publicbotport": "8443", "privatekey": "", "certificate": "", "useselfsignedcertificate": false, "sslterminated": false, "verboselogging": false}]