# 🚀 空調設備通知系統 - 快速使用指南

## 📋 監控設備
- 🌫️ 加濕器 (`humidifier.rdi_640hhchu_shi_ji`)
- 🌪️ 全熱交換器 (`fan.kpi_253hwquan_re_jiao_huan_qi_air_speed`)
- ❄️ 客廳冷氣 (`climate.ke_ting_leng_qi`)
- ❄️ 主臥冷氣 (`climate.zhu_wo_leng_qi`)
- ❄️ 次臥冷氣 (`climate.ci_wo_leng_qi`)

## 🔔 通知方式
- **NotifyHelper** - Home Assistant 內建通知
- **Synology Chat** - 群暉聊天室
- **Telegram** - Telegram Bot

---

## ⚡ 快速安裝步驟

### 1️⃣ 導入 Node-RED 流程
1. 打開 Node-RED 編輯器 (`http://樹莓派IP:1880`)
2. 點擊右上角選單 → 導入
3. 選擇 `complete-aircon-flow.json` 文件
4. 點擊「導入」

### 2️⃣ 配置 Home Assistant 連接
1. 雙擊任一個監控節點
2. 點擊「Server」旁的鉛筆圖示
3. 填入連接資訊：
   - **Base URL**: `http://localhost:8123` (如果在同一台樹莓派)
   - **Access Token**: 從 HA 用戶設定中生成

### 3️⃣ 配置通知服務

#### 🔧 Telegram 設定
1. 與 @BotFather 對話創建 Bot
2. 獲取 Bot Token
3. 雙擊「Telegram 發送」節點
4. 點擊「Bot」旁的鉛筆圖示
5. 輸入 Bot Token

#### 🔧 Synology Chat 設定
1. 在 Synology Chat 中創建 Incoming Webhook
2. 雙擊「Synology Chat 發送」節點
3. 將 URL 欄位替換為您的 Webhook URL

#### 🔧 NotifyHelper 設定
- 通常無需額外設定
- 確保 Home Assistant 中有 `notify.notify` 服務

### 4️⃣ 測試系統
1. 點擊「手動測試」節點左側的按鈕
2. 檢查「除錯」面板的輸出
3. 確認各通知平台收到測試訊息

### 5️⃣ 部署流程
1. 點擊右上角的「部署」按鈕
2. 系統開始監控設備狀態

---

## 📱 訊息範例

當設備狀態變化時，您會收到類似這樣的通知：

```
❄️ 客廳冷氣已冷卻模式
📍 位置：客廳
🕐 時間：2024/01/15 14:30:25
```

---

## 🔧 常見問題

**Q: 沒有收到通知？**
- 檢查 Home Assistant 連接是否正常
- 確認設備實體 ID 是否正確
- 查看 Node-RED 除錯面板

**Q: Telegram 通知失敗？**
- 確認 Bot Token 正確
- 確認 Chat ID 正確
- 檢查網路連接

**Q: 收到重複通知？**
- 系統有防重複機制，如果仍有問題請檢查流程邏輯

---

## 🎯 進階功能

### 自定義設備名稱
在「狀態處理器」節點中修改 `deviceNames` 對象：

```javascript
const deviceNames = {
    'climate.ke_ting_leng_qi': '客廳冷氣',  // 修改這裡
    // ... 其他設備
};
```

### 修改通知內容
在「狀態處理器」節點中修改 `fullMessage`：

```javascript
fullMessage: `${statusIcon} ${deviceName}已${stateName}\\n📍 位置：${location}\\n🕐 時間：${timestamp}`
```

### 添加靜音時間
在「狀態處理器」節點開頭添加：

```javascript
// 檢查靜音時間 (23:00-07:00)
const now = new Date();
const hour = now.getHours();
if (hour >= 23 || hour < 7) {
    return null; // 靜音時間不發送通知
}
```

---

## 📞 需要幫助？

1. 檢查 Node-RED 除錯面板
2. 查看 Home Assistant 日誌
3. 確認網路連接正常
4. 參考完整說明文檔：`README-aircon-notifications.md`

---

**🎉 享受您的智能家居通知系統！**
