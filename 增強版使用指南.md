# 🚀 空調設備通知系統 - 增強版使用指南

## ✨ 新功能特色

### 🔍 全面狀態監控
- ✅ **開關狀態** - 開啟/關閉
- ✅ **運行模式** - 冷氣/暖氣/除濕/送風/自動
- ✅ **溫度設定** - 監控溫度調整
- ✅ **風速變化** - 自動/低/中/高風速
- ✅ **擺風模式** - 垂直/水平/全方向擺風
- ✅ **當前溫度** - 顯示室內實際溫度

### 🔔 修正的通知服務
- ✅ **NotifyHelper** - 使用 `notify.notify_person` 服務（需要安裝 NotifyHelper 自定義整合）
- ✅ **Synology Chat** - `notify.synology_chat_bot_3`
- ✅ **Telegram** - `notify.telegram`

---

## 📋 監控設備

| 設備 | 實體 ID | 位置 | 監控內容 |
|------|---------|------|----------|
| 🌫️ **吊隱除濕機** | `humidifier.rdi_640hhchu_shi_ji` | 客廳 | 開關狀態 |
| 🌪️ **全熱交換器** | `fan.kpi_253hwquan_re_jiao_huan_qi_air_speed` | 全屋 | 風速變化 |
| ❄️ **客廳冷氣** | `climate.ke_ting_leng_qi` | 客廳 | 全功能監控 |
| ❄️ **主臥冷氣** | `climate.zhu_wo_leng_qi` | 主臥室 | 全功能監控 |
| ❄️ **次臥冷氣** | `climate.ci_wo_leng_qi` | 次臥室 | 全功能監控 |

---

## ⚡ 快速安裝

### 1️⃣ 導入流程
```
Node-RED → 選單 → 導入 → 選擇 aircon-notification-enhanced.json
```

### 2️⃣ 測試系統
點擊以下測試按鈕：
- **測試冷氣模式變更** - 模擬冷氣開啟和溫度設定
- **測試溫度調整** - 模擬溫度、風速、擺風變化
- **測試除濕機開關** - 模擬除濕機開關

### 3️⃣ 部署使用
點擊「部署」按鈕啟動監控

---

## 📱 通知訊息範例

### 🔄 模式變更通知
```
2024/01/15 14:30:25
❄️ 客廳冷氣
位置：客廳
模式：冷氣模式
設定溫度：24°C
目前溫度：26°C
```

### 🌡️ 溫度調整通知
```
2024/01/15 15:45:10
❄️ 主臥冷氣
位置：主臥室
設定溫度：22°C
風速：高風速
擺風：垂直擺風
目前溫度：25°C
```

### 🟢 開關狀態通知
```
2024/01/15 16:20:30
🟢 吊隱除濕機
位置：客廳
模式：開啟
```

---

## 🔧 進階功能

### 🚫 防重複通知
- 60秒冷卻期，避免頻繁通知
- 智能比較狀態和屬性變化
- 只在真正有變化時發送通知

### 📊 詳細狀態追蹤
系統會監控以下變化：
- **HVAC 模式**：off → heat → cool → auto → dry → fan_only
- **溫度設定**：任何溫度調整
- **風扇模式**：auto → low → medium → high → quiet
- **擺風模式**：off → vertical → horizontal → both

### 🎯 智能訊息格式
- 只顯示實際變化的項目
- 自動添加當前溫度（如果可用）
- 使用直觀的中文描述和圖示

---

## 🛠️ 故障排除

### ❌ NotifyHelper 沒收到通知？
**解決方案**：
1. **確認已安裝 NotifyHelper 整合**：
   - 前往 HACS → 整合 → 搜尋 "NotifyHelper"
   - 安裝 kukuxx/HA-NotifyHelper
   - 重啟 Home Assistant
   - 前往設定 → 裝置與服務 → 新增整合 → 搜尋 "NotifyHelper"

2. **檢查服務是否存在**：
   - 前往開發者工具 → 服務
   - 搜尋 `notify.notify_person`
   - 如果沒有此服務，表示 NotifyHelper 未正確安裝

3. **確認 person 實體**：
   - 確保 `person.ming` 實體存在
   - 可以在流程中修改為您的實際 person 實體 ID

4. **替代方案**：
   - 如果不想使用 NotifyHelper，可以改用 `notify.mobile_app_你的手機名稱`
   - 或使用 `notify.persistent_notification`（會在 HA 前端顯示）

### ❌ 收到太多重複通知？
**解決方案**：
- 系統已內建60秒冷卻期
- 檢查設備是否頻繁變化狀態
- 可以在狀態處理器中調整冷卻時間

### ❌ 某些狀態變化沒有通知？
**解決方案**：
- 檢查除錯面板的輸出
- 確認設備實體 ID 正確
- 查看錯誤日誌節點

---

## 🎨 自定義設定

### 修改冷卻時間
在「增強狀態處理器」中找到：
```javascript
// 檢查是否在冷卻期內（60秒）
if (lastNotification.time && (currentTime - lastNotification.time) < 60000) {
```
將 `60000` 改為其他值（毫秒）

### 添加新的屬性監控
在狀態處理器中添加：
```javascript
// 檢查新屬性變化
if (newAttrs.your_attribute !== oldAttrs.your_attribute) {
    attributeChanges.push({
        type: 'your_attribute',
        old: oldAttrs.your_attribute,
        new: newAttrs.your_attribute
    });
}
```

### 修改通知服務
如果 NotifyHelper 仍有問題，可以修改為：
```javascript
"action": "notify.mobile_app_你的手機名稱"
```

---

## 📞 技術支援

1. **檢查除錯面板** - 查看詳細的狀態變化日誌
2. **測試按鈕** - 使用內建的測試功能驗證
3. **錯誤日誌** - 查看系統錯誤和異常

---

**🎉 享受您的智能家居全方位監控系統！**

現在您可以監控空調設備的每一個細微變化，包括溫度調整、模式切換、風速變化等，讓您對家中的空調系統有完全的掌控！
